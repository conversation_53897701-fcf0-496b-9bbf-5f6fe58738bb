# 蒙版可见性和表情键盘修复

## 问题分析

修改层级管理后出现了三个新问题：

1. **红色蒙版看不见了** - containerView的背景色覆盖了红色蒙版
2. **评论又可以交互了** - 蒙版没有正常阻挡交互
3. **表情键盘不会正常弹出** - containerView遮挡了表情键盘

## 问题根因

### 1. 红色蒙版不可见的原因

**视图层级**：
```
view
├── inputMaskView (红色蒙版，透明度0.1)
└── containerView (背景色#F5F5F5，不透明) ← 覆盖了红色蒙版
```

**问题**：
- `containerView`的背景色是`UIColor(hex: "#F5F5F5")`（浅灰色）
- `containerView`在红色蒙版之上，完全遮挡了红色蒙版
- 用户看不到红色蒙版，以为蒙版没有工作

### 2. 表情键盘被遮挡的原因

**视图层级**：
```
view
├── inputMaskView (红色蒙版)
├── emojiKeyboardView (表情键盘) ← 被containerView遮挡
└── containerView (容器视图)
```

**问题**：
- 表情键盘添加到`view`上
- `containerView`在表情键盘之上，遮挡了表情键盘
- 用户看不到表情键盘

## 修复方案

### 1. 修复红色蒙版可见性

**解决方案**：动态调整`containerView`的背景色

```swift
switch inputPanelState {
case .keyboard, .emoji, .atPanel:
    // 有输入面板时，让containerView背景透明，红色蒙版可以透过来
    containerView.backgroundColor = UIColor.clear
    
case .none:
    // 无输入面板时，恢复containerView的原始背景色
    containerView.backgroundColor = UIColor(hex: "#F5F5F5")
}
```

**效果**：
- 输入状态下：`containerView`透明，红色蒙版可见
- 正常状态下：`containerView`有背景色，正常显示

### 2. 修复表情键盘层级

**解决方案**：确保表情键盘在`containerView`之上

```swift
// 表情键盘要在containerView之上
if let emojiView = emojiKeyboardView {
    view.bringSubviewToFront(emojiView)  // 3. 表情键盘
}
```

**层级顺序**：
```
view
├── inputMaskView (红色蒙版) ← 1. 底层
├── containerView (容器视图) ← 2. 中层
├── emojiKeyboardView (表情键盘) ← 3. 上层
├── atPanelMaskView (@面板蒙版) ← 4. 更上层
└── atPanelView (@面板) ← 5. 最上层
```

## 修复后的完整层级管理

### 正确的视图层级（从下到上）：

```
view
├── inputMaskView (红色蒙版，全屏覆盖)
├── containerView (容器视图，输入时背景透明)
│   ├── headerView (标题栏)
│   ├── tableView (评论列表)
│   ├── decorativeInputBar (装饰输入框)
│   └── commentInputBar (真实输入框)
├── emojiKeyboardView (表情键盘)
├── atPanelMaskView (@面板蒙版)
└── atPanelView (@面板)
```

### 动态背景色管理：

**输入状态下**：
- `containerView.backgroundColor = UIColor.clear`
- 红色蒙版透过透明的`containerView`可见
- 用户可以看到红色蒙版，知道评论列表被阻挡

**正常状态下**：
- `containerView.backgroundColor = UIColor(hex: "#F5F5F5")`
- 恢复正常的UI外观
- 评论列表正常显示

## 关键修复代码

### 1. 蒙版可见性管理

```swift
private func updateInputMaskVisibility() {
    switch inputPanelState {
    case .keyboard, .emoji, .atPanel:
        maskView.isHidden = false
        // 关键修复：让containerView背景透明，红色蒙版可以透过来
        containerView.backgroundColor = UIColor.clear
        
    case .none:
        maskView.isHidden = true
        // 恢复containerView的原始背景色
        containerView.backgroundColor = UIColor(hex: "#F5F5F5")
    }
}
```

### 2. 完整层级管理

```swift
private func ensureInputViewsAboveMask() {
    view.bringSubviewToFront(maskView)      // 1. 红色蒙版
    view.bringSubviewToFront(containerView) // 2. 容器视图
    
    // 表情键盘要在containerView之上
    if let emojiView = emojiKeyboardView, !emojiView.isHidden {
        view.bringSubviewToFront(emojiView) // 3. 表情键盘
    }
    
    // @面板在最上层
    if let atPanelMask = atPanelMaskView, !atPanelMask.isHidden {
        view.bringSubviewToFront(atPanelMask) // 4. @面板蒙版
    }
    if let atPanel = atPanelView, !atPanel.isHidden {
        view.bringSubviewToFront(atPanel)     // 5. @面板
    }
}
```

## 预期修复效果

### ✅ 修复后应该实现：

1. **红色蒙版正常可见**：
   - 输入状态下，红色蒙版透过透明的`containerView`可见
   - 用户可以清楚看到红色蒙版覆盖评论列表
   - 点击红色蒙版可以收起相应面板

2. **评论交互正确阻挡**：
   - 红色蒙版阻挡评论列表的所有交互
   - 用户无法点击评论头像、点赞等
   - 只能与输入框和面板交互

3. **表情键盘正常显示**：
   - 点击表情按钮，表情键盘正常弹出
   - 表情键盘在`containerView`之上，完全可见
   - 表情键盘功能正常工作

4. **UI外观正常**：
   - 正常状态下，`containerView`有背景色，UI正常
   - 输入状态下，`containerView`透明，红色蒙版可见
   - 状态切换时背景色正确变化

## 测试验证

### 1. 测试红色蒙版可见性
1. 点击输入框，键盘弹起
2. 应该看到红色蒙版覆盖评论列表 ✅
3. 蒙版应该是半透明红色，清晰可见 ✅
4. 点击红色蒙版，键盘收起 ✅

### 2. 测试评论交互阻挡
1. 键盘弹起状态下
2. 尝试点击评论头像 → 无反应 ✅
3. 尝试点击点赞按钮 → 无反应 ✅
4. 尝试滑动评论列表 → 无反应 ✅

### 3. 测试表情键盘
1. 点击表情按钮
2. 表情键盘应该正常弹出 ✅
3. 表情键盘应该完全可见，不被遮挡 ✅
4. 点击表情，正常插入到输入框 ✅

### 4. 测试@面板
1. 输入@符号，@面板显示
2. @面板应该在最上层，完全可见 ✅
3. 红色蒙版仍然可见 ✅
4. 所有功能正常工作 ✅

## 关键技术点

### 1. 动态背景色管理
- 根据状态动态调整`containerView`背景色
- 输入时透明，正常时有背景色
- 确保UI外观和功能的平衡

### 2. 多层级视图管理
- 红色蒙版（底层阻挡）
- 容器视图（中层内容）
- 表情键盘（上层功能）
- @面板（最上层功能）

### 3. 状态驱动的层级调整
- 每次状态变化时重新调整层级
- 确保所有视图在正确位置
- 多重保障机制

现在红色蒙版应该可见，表情键盘也应该正常工作了！
