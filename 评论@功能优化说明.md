# 评论@功能用户体验优化

## 问题描述

原有的评论@功能存在以下用户体验问题：

1. **第一次点击空白区域**：键盘收起，但@View和输入框保持原位
2. **再次点击空白区域**：键盘收起，真输入框收起，假输入框出现，但@View没有收起，还有异常间距
3. **@View中Cell的点击范围较小**，用户体验不佳

## 优化方案

### 1. 背景点击逻辑优化

**原有逻辑**：
```swift
@objc private func backgroundTapped() {
    // 直接关闭弹窗
    dismiss(animated: true)
}
```

**优化后逻辑**：
```swift
@objc private func backgroundTapped() {
    // 根据当前状态决定行为
    switch inputPanelState {
    case .keyboard:
        // 键盘弹出时，第一次点击收起键盘
        inputPanelState = .none
    case .emoji:
        // 表情面板弹出时，收起表情面板
        inputPanelState = .none
    case .atPanel:
        // @面板弹出时，收起@面板，回到键盘状态
        resetAtPanelState()
        inputPanelState = .keyboard
    case .none:
        // 都收起时，关闭整个弹窗
        dismiss(animated: true)
    }
}
```

### 2. 键盘隐藏时的状态管理

**优化内容**：
- 键盘隐藏时自动清理@提及状态
- 确保@面板在键盘收起时也被正确隐藏

```swift
@objc private func keyboardWillHide(_ notification: Notification) {
    if inputPanelState == .emoji || inputPanelState == .atPanel {
        // 不回到底部，等待自定义面板弹出
    } else {
        // 键盘隐藏时，清理@状态
        resetAtPanelState()
        inputPanelState = .none
    }
}
```

### 3. 输入面板状态切换优化

**优化内容**：
- 在所有状态切换时正确处理@面板的显示/隐藏
- 统一使用`resetAtPanelState()`方法管理@状态

```swift
case .none:
    // 收起时确保@面板也被隐藏
    hideAtPanel()
    resetAtPanelState()
    
case .keyboard:
    // 从非@面板状态切换时隐藏@面板
    if old != .atPanel {
        hideAtPanel()
    }
    
case .emoji:
    // 切换到表情面板时重置@状态
    resetAtPanelState()
```

### 4. @View Cell点击范围优化

**原有Cell尺寸**：74x70
**优化后Cell尺寸**：80x76

**新增功能**：
- 添加背景高亮视图，提供视觉反馈
- 增加点击时的缩放动画效果
- 扩大实际点击区域

```swift
class MentionUserCell: UICollectionViewCell {
    private let backgroundHighlightView = UIView()
    
    // 点击反馈效果
    override var isHighlighted: Bool {
        didSet {
            UIView.animate(withDuration: 0.1) {
                self.backgroundHighlightView.isHidden = !self.isHighlighted
                self.transform = self.isHighlighted ? 
                    CGAffineTransform(scaleX: 0.95, y: 0.95) : .identity
            }
        }
    }
}
```

### 5. 统一的@面板状态管理

**新增方法**：
```swift
private func resetAtPanelState() {
    if isMentioning {
        hideAtPanel()
        isMentioning = false
    }
}

private func hideAtPanel() {
    atPanelView?.isHidden = true
    // 恢复占位文字
    // 重置输入属性
    // 清理@相关数据
    mentionUsersData.removeAll()
    mentionCollectionView?.reloadData()
}
```

## 优化效果

1. **更合理的交互逻辑**：
   - 第一次点击空白：收起键盘，保持输入状态
   - @面板显示时点击空白：收起@面板，回到键盘状态
   - 完全收起后点击空白：关闭评论弹窗

2. **更好的状态管理**：
   - @面板状态与键盘状态正确同步
   - 避免出现@面板残留的情况
   - 消除异常间距问题

3. **更佳的用户体验**：
   - @View Cell点击范围更大
   - 添加视觉反馈效果
   - 交互更加直观和流畅

## 进一步优化（第二轮）

### 6. @面板上方蒙版机制

**新增功能**：
- 在@面板上方添加透明蒙版`atPanelMaskView`
- 点击蒙版收起@面板，但保持键盘弹出状态
- @面板内部和输入框区域不触发蒙版点击

```swift
// @面板上方蒙版点击处理
@objc private func atPanelMaskTapped() {
    // 收起@面板，但保持键盘弹出状态
    resetAtPanelState()
    inputPanelState = .keyboard
}
```

### 7. 键盘管理优化

**优化逻辑**：
- @面板打开时，背景点击不收起键盘
- 只有特定方式才能收起键盘：
  - 第三方键盘的收起按钮
  - 系统键盘的收起操作
  - 输入框失去焦点

```swift
case .atPanel:
    // @面板打开时，背景点击不做任何操作
    return
```

### 8. 手势代理精细化控制

**优化内容**：
- @面板蒙版手势只在@面板上方区域生效
- @面板内部和输入框区域不触发蒙版点击
- 防止意外的键盘收起操作

```swift
func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
    // 如果是@面板蒙版的手势
    if touch.view == atPanelMaskView {
        // 精确判断点击区域
        let touchPoint = touch.location(in: view)
        // 排除@面板和输入框区域
        return !panelFrame.contains(touchPoint) && !inputFrame.contains(touchPoint)
    }
    // 其他逻辑...
}
```

### 9. 状态同步优化

**改进内容**：
- 键盘隐藏时根据状态决定行为
- @面板状态下键盘收起意味着@面板也收起
- 确保蒙版与@面板状态同步

```swift
@objc private func keyboardWillHide(_ notification: Notification) {
    if inputPanelState == .atPanel {
        // @面板状态：键盘收起意味着@面板也要收起
        resetAtPanelState()
        inputPanelState = .none
    }
    // 其他状态处理...
}
```

## 最终交互逻辑

### @面板打开状态下的交互：

1. **点击@面板上方区域** → 收起@面板，保持键盘
2. **点击@面板内部** → 无操作，保持当前状态
3. **点击输入框区域** → 无操作，保持当前状态
4. **点击背景其他区域** → 无操作，避免意外收起键盘
5. **第三方键盘收起按钮** → 收起键盘和@面板
6. **选择@用户** → 插入用户名，回到键盘状态

### 优化效果：

- ✅ 避免了奇怪的键盘管理问题
- ✅ 提供了精确的@面板控制
- ✅ 保持了直观的用户交互体验
- ✅ 防止了意外的状态切换

## 测试建议

1. 测试@按钮点击后的面板显示
2. 测试@面板上方蒙版的点击行为
3. 测试@面板内部点击不触发收起
4. 测试输入框区域点击不触发蒙版
5. 测试第三方键盘的收起按钮
6. 测试各种状态下的背景点击行为
7. 测试@View中用户Cell的点击响应
8. 测试键盘收起时的状态清理
9. 测试状态切换时的动画效果
