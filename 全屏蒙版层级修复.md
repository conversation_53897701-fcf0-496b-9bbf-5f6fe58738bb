# 全屏蒙版层级修复

## 问题描述

使用全屏蒙版后，输入框被红色蒙版覆盖，导致用户无法看到和交互输入框。

## 问题分析

### 全屏蒙版的挑战：
使用全屏蒙版虽然解决了间隙问题，但带来了新的层级管理挑战：

**全屏蒙版的特点**：
- 覆盖整个屏幕（包括输入框区域）
- 需要确保输入框在蒙版之上才能可见和可交互
- 需要精确的层级管理

**错误的层级顺序**：
```
view
├── containerView (评论列表)
├── decorativeInputBar (装饰输入框)
├── commentInputBar (真实输入框)
├── inputMaskView (全屏红色蒙版) ← 覆盖了输入框 ❌
├── atPanelMaskView (@面板蒙版)
└── atPanelView (@面板)
```

## 修复方案

### 正确的层级管理

**修复后的层级顺序**：
```
view
├── containerView (评论列表)
├── inputMaskView (全屏红色蒙版) ← 底层
├── decorativeInputBar (装饰输入框) ← 在蒙版之上
├── commentInputBar (真实输入框) ← 在蒙版之上
├── atPanelMaskView (@面板蒙版) ← 在输入框之上
└── atPanelView (@面板) ← 最上层
```

### 关键修复点

#### 1. 输入框蒙版显示时的层级管理

**修复后的代码**：
```swift
private func updateInputMaskVisibility() {
    switch inputPanelState {
    case .keyboard, .emoji, .atPanel:
        maskView.isHidden = false
        // 确保正确的层级顺序（从下到上）
        view.bringSubviewToFront(maskView)           // 1. 全屏红色蒙版
        view.bringSubviewToFront(decorativeInputBar) // 2. 装饰输入框（在蒙版之上）
        view.bringSubviewToFront(commentInputBar)    // 3. 真实输入框（在蒙版之上）
        view.bringSubviewToFront(atPanelMaskView)    // 4. @面板蒙版
        view.bringSubviewToFront(atPanelView)        // 5. @面板（最上层）
    }
}
```

#### 2. @面板显示时的层级管理

**修复后的代码**：
```swift
// 确保完整的层级顺序
view.bringSubviewToFront(inputMaskView!)      // 1. 全屏红色蒙版
view.bringSubviewToFront(decorativeInputBar)  // 2. 装饰输入框
view.bringSubviewToFront(commentInputBar)     // 3. 真实输入框
view.bringSubviewToFront(mask)                // 4. @面板蒙版（蓝色）
view.bringSubviewToFront(panel)               // 5. @面板（最上层）
```

## 修复后的完整布局

### 视觉层级（从下到上）：

```
┌─────────────────────────────────────┐
│ 全屏红色蒙版                        │ ← 覆盖整个屏幕，阻挡所有交互
│ (覆盖评论列表、状态栏等所有区域)    │
├─────────────────────────────────────┤
│ 装饰输入框/真实输入框               │ ← 在红色蒙版之上，可见可交互
├─────────────────────────────────────┤
│ @面板蒙版（蓝色，@面板时显示）      │ ← 在输入框之上
├─────────────────────────────────────┤
│ @面板（白色，@面板时显示）          │ ← 最上层，可见可交互
└─────────────────────────────────────┘
```

### 交互逻辑：

1. **红色蒙版区域**：
   - 覆盖整个屏幕
   - 阻挡所有评论列表交互
   - 点击收起键盘

2. **输入框区域**：
   - 在红色蒙版之上
   - 完全可见和可交互
   - 可以正常输入文字

3. **@面板区域**：
   - 在输入框之上
   - 显示用户列表
   - 可以正常选择用户

4. **蓝色蒙版区域**：
   - 在@面板显示时出现
   - 点击收起@面板但保持键盘

## 预期修复效果

### ✅ 修复后应该实现：

1. **全屏蒙版正常工作**：
   - 红色蒙版覆盖整个屏幕，无任何间隙
   - 完全阻挡评论列表交互

2. **输入框完全可见**：
   - 装饰输入框在红色蒙版之上，完全可见
   - 真实输入框在红色蒙版之上，完全可见
   - 输入框可以正常交互（点击、输入、按钮等）

3. **@面板正常显示**：
   - @面板在输入框之上，完全可见
   - @面板可以正常交互（选择用户等）
   - 蓝色蒙版正常工作

4. **层级关系清晰**：
   - 每个元素都在正确的层级
   - 不会出现遮挡问题
   - 交互逻辑正确

## 测试验证

### 1. 测试输入框可见性
1. 点击输入框，键盘弹起
2. 应该看到红色蒙版覆盖整个屏幕
3. 输入框应该完全可见，不被红色蒙版遮挡 ✅
4. 输入框应该可以正常点击和输入 ✅

### 2. 测试@面板层级
1. 输入@符号，@面板显示
2. 应该看到：红色蒙版（全屏）+ 输入框（可见）+ 蓝色蒙版 + 白色@面板
3. @面板应该在最上层，完全可见 ✅
4. 可以正常选择@面板中的用户 ✅

### 3. 测试交互逻辑
1. 点击红色蒙版区域 → 键盘收起 ✅
2. 点击输入框 → 正常弹出键盘 ✅
3. 点击@按钮 → 正常插入@符号 ✅
4. 点击蓝色蒙版 → @面板收起，键盘保持 ✅

### 4. 测试边界情况
1. 快速切换不同状态 → 层级应该始终正确
2. 旋转屏幕 → 全屏蒙版应该正确覆盖
3. 不同设备尺寸 → 层级关系应该保持正确

## 关键技术点

### 1. bringSubviewToFront的使用
```swift
// 正确的调用顺序很重要
view.bringSubviewToFront(maskView)        // 先调用的在下层
view.bringSubviewToFront(inputBar)        // 后调用的在上层
```

### 2. 全屏蒙版的约束
```swift
// 简单的全屏约束
maskView.topAnchor.constraint(equalTo: view.topAnchor)
maskView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
```

### 3. 状态驱动的层级管理
- 每次状态变化时重新调整层级
- 确保关键元素始终在正确位置
- 避免层级混乱

现在输入框应该在红色蒙版之上，完全可见和可交互！
