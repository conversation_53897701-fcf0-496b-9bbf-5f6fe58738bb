# 蒙版位置和层级修复说明

## 问题分析

### 原始问题
1. **蒙版位置错误**：半透明红色蒙版在键盘上方的整个屏幕，应该在输入框上方
2. **@面板层级错误**：@面板和@面板蒙版应该在输入框蒙版之上

### 根本原因
1. **输入框蒙版约束错误**：底部约束到`view.safeAreaLayoutGuide.bottomAnchor`，导致覆盖整个屏幕
2. **@面板层级混乱**：@面板添加到`containerView`，输入框蒙版添加到`view`，层级不统一

## 修复方案

### 1. 修复输入框蒙版位置

**修复前**：
```swift
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120)
```
- 问题：相对于屏幕底部，覆盖整个屏幕

**修复后**：
```swift
maskView.bottomAnchor.constraint(equalTo: decorativeInputBar.topAnchor)
```
- 解决：相对于输入框顶部，只覆盖输入框上方区域

### 2. 统一@面板层级

**修复前**：
```swift
// @面板蒙版和@面板都添加到containerView
containerView.addSubview(maskView)
containerView.addSubview(panel)
```
- 问题：与输入框蒙版（在view上）层级不统一

**修复后**：
```swift
// @面板蒙版和@面板都添加到view
view.addSubview(maskView)
view.addSubview(panel)
```
- 解决：所有蒙版和面板都在同一层级，便于管理

### 3. 优化层级管理

**修复后的层级顺序**（从下到上）：
```swift
private func updateInputMaskVisibility() {
    view.bringSubviewToFront(maskView)           // 1. 输入框蒙版（红色）
    view.bringSubviewToFront(decorativeInputBar) // 2. 装饰输入框
    view.bringSubviewToFront(commentInputBar)    // 3. 真实输入框
    view.bringSubviewToFront(atPanelMaskView)    // 4. @面板蒙版（蓝色）
    view.bringSubviewToFront(atPanelView)        // 5. @面板（最上层）
}
```

### 4. 添加调试可视化

**输入框蒙版**：
```swift
maskView.backgroundColor = UIColor.red.withAlphaComponent(0.1) // 半透明红色
```

**@面板蒙版**：
```swift
maskView.backgroundColor = UIColor.blue.withAlphaComponent(0.1) // 半透明蓝色
```

## 正确的视图层级结构

### 修复后的完整层级（从下到上）：

```
view
├── backgroundView (背景遮罩，关闭整个弹窗)
├── containerView (评论列表容器)
│   ├── headerView (标题栏)
│   ├── tableView (评论列表)
│   └── ...
├── inputMaskView (输入框蒙版，红色，遮挡评论列表) ⭐
├── decorativeInputBar (装饰输入框)
├── commentInputBar (真实输入框)
├── atPanelMaskView (@面板蒙版，蓝色，收起@面板) ⭐
└── atPanelView (@面板，最上层) ⭐
```

## 约束关系

### 输入框蒙版约束：
```swift
maskView.topAnchor.constraint(equalTo: view.topAnchor)
maskView.leftAnchor.constraint(equalTo: view.leftAnchor)
maskView.rightAnchor.constraint(equalTo: view.rightAnchor)
maskView.bottomAnchor.constraint(equalTo: decorativeInputBar.topAnchor) // 关键修复
```

### @面板蒙版约束：
```swift
maskView.topAnchor.constraint(equalTo: view.topAnchor)
maskView.leftAnchor.constraint(equalTo: view.leftAnchor)
maskView.rightAnchor.constraint(equalTo: view.rightAnchor)
maskView.bottomAnchor.constraint(equalTo: decorativeInputBar.topAnchor) // 与输入框蒙版相同
```

### @面板约束：
```swift
panel.leftAnchor.constraint(equalTo: view.leftAnchor)
panel.rightAnchor.constraint(equalTo: view.rightAnchor)
panel.bottomAnchor.constraint(equalTo: decorativeInputBar.topAnchor)
panel.heightAnchor.constraint(equalToConstant: 104)
```

## 预期修复效果

### ✅ 修复后应该看到：

1. **输入框蒙版正确位置**：
   - 键盘弹出时，半透明红色蒙版只覆盖输入框上方区域
   - 不覆盖键盘区域
   - 不覆盖输入框本身

2. **@面板层级正确**：
   - 输入@后，@面板显示在输入框蒙版之上
   - @面板有自己的半透明蓝色蒙版
   - 点击蓝色蒙版收起@面板，点击红色蒙版收起键盘

3. **交互逻辑正确**：
   - 键盘状态：点击红色蒙版收起键盘
   - @面板状态：点击蓝色蒙版收起@面板但保持键盘，点击红色蒙版（如果可见）收起键盘

## 测试验证

### 1. 测试输入框蒙版位置
1. 点击输入框弹出键盘
2. 应该看到半透明红色蒙版只在输入框上方
3. 键盘区域不应该被蒙版覆盖

### 2. 测试@面板层级
1. 输入@符号，@面板显示
2. 应该看到白色@面板和半透明蓝色蒙版
3. @面板应该在红色蒙版之上

### 3. 测试交互逻辑
1. 键盘状态：点击红色蒙版 → 键盘收起
2. @面板状态：点击蓝色蒙版 → @面板收起，键盘保持
3. @面板状态：点击红色蒙版（如果可见）→ 键盘收起

## 控制台日志

修复后应该看到：

```
[VideoComment] 输入框蒙版添加到视图，底部约束到输入框顶部
[VideoComment] 显示输入框蒙版，层级已调整：输入框蒙版 < 输入框 < @面板蒙版 < @面板
[VideoComment] @面板蒙版创建完成，添加到view层级
[VideoComment] @面板创建完成，添加到view层级
```

现在蒙版位置和层级都应该正确了！
