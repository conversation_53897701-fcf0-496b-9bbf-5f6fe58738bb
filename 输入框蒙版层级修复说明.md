# 输入框蒙版层级修复说明

## 问题分析

### 原始问题
1. **蒙版不能收起键盘** - 点击蒙版没有反应
2. **遮挡不了评论中的头像点击** - 蒙版层级不正确，在评论列表下方

### 根本原因
**视图层级错误**：蒙版被添加到`view`上，但`containerView`（包含评论列表）是在蒙版之后添加的，导致蒙版在评论列表下方。

## 修复方案

### 1. 调整蒙版创建时机

**修复前**：
```swift
private func setupInputMaskView() {
    let maskView = UIView()
    view.addSubview(maskView) // 立即添加，但此时containerView还未创建
    // ...
}
```

**修复后**：
```swift
private func setupInputMaskView() {
    let maskView = UIView()
    // 先不添加到视图，等containerView创建后再添加
    // ...
}

private func addInputMaskToView() {
    guard let maskView = inputMaskView else { return }
    view.addSubview(maskView) // 在containerView之后添加，确保层级正确
    // ...
}
```

### 2. 优化层级管理

**修复后的层级顺序**：
```swift
private func updateInputMaskVisibility() {
    switch inputPanelState {
    case .keyboard, .emoji, .atPanel:
        maskView.isHidden = false
        // 确保正确的层级顺序
        view.bringSubviewToFront(maskView)           // 蒙版在评论列表之上
        view.bringSubviewToFront(decorativeInputBar) // 输入框在蒙版之上
        view.bringSubviewToFront(commentInputBar)    // 输入框在蒙版之上
        view.bringSubviewToFront(atPanelView)        // @面板在蒙版之上
    }
}
```

### 3. 增强点击处理

**修复后**：
```swift
@objc private func inputMaskTapped() {
    print("[VideoComment] 输入框蒙版被点击，当前状态: \(inputPanelState)")
    
    switch inputPanelState {
    case .keyboard:
        print("[VideoComment] 收起键盘")
        inputFieldRef?.resignFirstResponder() // 明确收起键盘
        inputPanelState = .none
    case .atPanel:
        print("[VideoComment] 收起@面板，保持键盘")
        resetAtPanelState()
        inputPanelState = .keyboard
    case .emoji:
        print("[VideoComment] 收起表情面板")
        inputPanelState = .none
    case .none:
        print("[VideoComment] 当前无输入面板，不做操作")
        break
    }
}
```

### 4. 添加调试可视化

**临时调试背景**：
```swift
// 临时添加半透明背景，方便调试蒙版位置
maskView.backgroundColor = UIColor.red.withAlphaComponent(0.1)
```

## 正确的视图层级结构

### 修复后的层级（从下到上）：

```
view
├── backgroundView (背景遮罩，用于关闭整个弹窗)
├── containerView (评论列表容器)
│   ├── headerView (标题栏)
│   ├── tableView (评论列表)
│   └── ...
├── inputMaskView (输入框蒙版，遮挡评论列表交互) ⭐ 关键层级
├── decorativeInputBar (装饰输入框)
├── commentInputBar (真实输入框)
├── atPanelMaskView (@面板蒙版)
└── atPanelView (@面板)
```

## 预期修复效果

### ✅ 修复后应该实现：

1. **蒙版正确遮挡**：
   - 键盘弹出时，蒙版显示（半透明红色，便于调试）
   - 点击评论列表中的头像无反应（被蒙版阻挡）
   - 点击评论列表中的其他元素无反应（被蒙版阻挡）

2. **蒙版点击收起**：
   - 点击蒙版区域，控制台输出点击日志
   - 普通键盘状态：键盘收起
   - @面板状态：@面板收起，键盘保持
   - 表情面板状态：表情面板收起

3. **输入功能正常**：
   - 输入框可以正常点击和输入
   - @按钮、表情按钮等功能正常
   - @面板、表情面板正常显示

## 调试步骤

### 1. 验证蒙版显示
1. 点击输入框弹出键盘
2. 观察是否出现半透明红色蒙版
3. 蒙版应该覆盖评论列表区域

### 2. 验证蒙版遮挡
1. 键盘弹出状态下
2. 尝试点击评论中的头像
3. 应该无反应（被蒙版阻挡）

### 3. 验证蒙版点击
1. 点击蒙版区域
2. 观察控制台日志输出
3. 键盘应该收起，蒙版消失

### 4. 验证@面板
1. 输入@符号，@面板显示
2. 点击蒙版，@面板收起但键盘保持
3. 再次点击蒙版，键盘收起

## 控制台日志

修复后应该看到以下日志：

```
[VideoComment] 输入框蒙版创建完成
[VideoComment] 输入框蒙版添加到视图，层级正确
[VideoComment] 显示输入框蒙版，层级已调整
[VideoComment] 输入框蒙版被点击，当前状态: keyboard
[VideoComment] 收起键盘
[VideoComment] 隐藏输入框蒙版
```

## 后续优化

调试完成后，可以将蒙版背景改为透明：
```swift
maskView.backgroundColor = UIColor.clear
```

现在蒙版应该能正确工作：遮挡评论列表交互，点击收起键盘！
