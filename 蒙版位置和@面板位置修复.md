# 蒙版位置和@面板位置修复

## 问题分析

### 用户反馈的问题
1. **@面板位置错误**：@面板没有弹到键盘上方
2. **输入框蒙版位置错误**：应该在输入框下方（覆盖评论列表），而不是遮住输入框

### 原始设计理解错误
我之前理解错了需求：
- **错误理解**：蒙版在输入框上方，阻挡输入框上方的内容
- **正确理解**：蒙版在输入框下方，阻挡评论列表的交互，但不影响输入框本身

## 修复方案

### 1. 修复输入框蒙版位置

**修复前**：
```swift
// 蒙版覆盖输入框上方区域（错误）
maskView.topAnchor.constraint(equalTo: view.topAnchor)
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120)
```

**修复后**：
```swift
// 蒙版覆盖评论列表区域（输入框下方）
maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60) // 从containerView顶部开始
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120) // 到输入框上方
```

### 2. 修复@面板位置跟随键盘

**问题**：@面板使用固定位置，不跟随键盘高度变化

**修复方案**：
1. **添加动态约束**：
   ```swift
   private var atPanelBottomConstraint: NSLayoutConstraint? // @面板底部约束
   ```

2. **创建时使用键盘高度**：
   ```swift
   atPanelBottomConstraint = panel.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -lastKeyboardHeight)
   ```

3. **键盘变化时更新位置**：
   ```swift
   @objc private func keyboardWillShow(_ notification: Notification) {
       // ...
       atPanelBottomConstraint?.constant = -keyboardHeight
       // ...
   }
   ```

### 3. 修复@面板蒙版位置

**修复后**：
```swift
// @面板蒙版覆盖评论列表区域，到@面板顶部
maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60)
maskView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -(lastKeyboardHeight + 104))
```

## 正确的布局逻辑

### 视图区域划分：

```
┌─────────────────────────────────────┐
│ 状态栏 + 安全区域                    │
├─────────────────────────────────────┤ ← view.safeAreaLayoutGuide.topAnchor
│ 空白区域 (60pt)                     │
├─────────────────────────────────────┤ ← containerView.topAnchor
│                                     │
│ 评论列表区域                        │ ← 输入框蒙版覆盖区域 (红色)
│ (被蒙版阻挡交互)                    │ ← @面板蒙版覆盖区域 (蓝色)
│                                     │
├─────────────────────────────────────┤ ← @面板顶部
│ @面板 (104pt高)                     │ ← 白色@面板
├─────────────────────────────────────┤ ← 输入框顶部
│ 输入框区域                          │ ← 不被蒙版覆盖
├─────────────────────────────────────┤ ← 键盘顶部
│ 键盘区域                            │
└─────────────────────────────────────┘
```

### 蒙版覆盖逻辑：

1. **输入框蒙版（红色）**：
   - 覆盖：评论列表区域
   - 不覆盖：输入框、@面板、键盘
   - 作用：阻挡评论列表交互

2. **@面板蒙版（蓝色）**：
   - 覆盖：评论列表区域（与输入框蒙版重叠）
   - 不覆盖：@面板、输入框、键盘
   - 作用：收起@面板的点击区域

## 约束详情

### 输入框蒙版约束：
```swift
maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60)
maskView.leftAnchor.constraint(equalTo: view.leftAnchor)
maskView.rightAnchor.constraint(equalTo: view.rightAnchor)
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120)
```

### @面板约束：
```swift
panel.leftAnchor.constraint(equalTo: view.leftAnchor)
panel.rightAnchor.constraint(equalTo: view.rightAnchor)
panel.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -lastKeyboardHeight) // 动态跟随键盘
panel.heightAnchor.constraint(equalToConstant: 104)
```

### @面板蒙版约束：
```swift
maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60)
maskView.leftAnchor.constraint(equalTo: view.leftAnchor)
maskView.rightAnchor.constraint(equalTo: view.rightAnchor)
maskView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -(lastKeyboardHeight + 104)) // 到@面板顶部
```

## 预期修复效果

### ✅ 修复后应该实现：

1. **输入框蒙版正确位置**：
   - 红色蒙版只覆盖评论列表区域
   - 不遮挡输入框本身
   - 点击蒙版收起键盘

2. **@面板正确跟随键盘**：
   - @面板显示在键盘正上方
   - 键盘高度变化时，@面板位置自动调整
   - @面板高度固定104pt

3. **@面板蒙版正确位置**：
   - 蓝色蒙版覆盖评论列表区域
   - 不遮挡@面板和输入框
   - 点击蒙版收起@面板但保持键盘

4. **交互逻辑正确**：
   - 键盘状态：点击红色蒙版收起键盘
   - @面板状态：点击蓝色蒙版收起@面板，点击红色蒙版收起键盘
   - 评论列表被蒙版阻挡，无法交互

## 测试验证

### 1. 测试输入框蒙版
1. 点击输入框弹出键盘
2. 应该看到红色蒙版覆盖评论列表区域
3. 输入框本身不被遮挡
4. 点击红色蒙版，键盘收起

### 2. 测试@面板位置
1. 输入@符号
2. @面板应该显示在键盘正上方
3. @面板高度104pt，白色背景
4. 蓝色蒙版覆盖评论列表区域

### 3. 测试@面板交互
1. @面板显示时，点击蓝色蒙版
2. @面板收起，键盘保持
3. 再点击红色蒙版，键盘收起

### 4. 测试评论列表阻挡
1. 键盘弹出时，尝试点击评论头像
2. 应该无反应（被蒙版阻挡）
3. 键盘收起后，评论列表恢复正常交互

现在蒙版和@面板的位置都应该正确了！
