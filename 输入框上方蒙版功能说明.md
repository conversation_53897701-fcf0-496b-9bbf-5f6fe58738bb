# 输入框上方蒙版功能说明

## 功能需求

在输入框上方添加一个蒙版，实现以下功能：

1. **正常输入时**：点击蒙版收起键盘
2. **防止误操作**：编辑时不能交互下方评论列表
3. **@面板时**：点击蒙版收起@面板但保持键盘
4. **表情面板时**：点击蒙版收起表情面板

## 技术实现

### 1. 蒙版视图创建

```swift
private var inputMaskView: UIView? // 输入框上方的蒙版

private func setupInputMaskView() {
    let maskView = UIView()
    maskView.backgroundColor = UIColor.clear // 透明蒙版
    maskView.isHidden = true // 默认隐藏
    view.addSubview(maskView)
    
    // 蒙版覆盖输入框上方的区域，但不覆盖输入框本身
    NSLayoutConstraint.activate([
        maskView.topAnchor.constraint(equalTo: view.topAnchor),
        maskView.leftAnchor.constraint(equalTo: view.leftAnchor),
        maskView.rightAnchor.constraint(equalTo: view.rightAnchor),
        maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120)
    ])
    
    // 添加点击手势
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(inputMaskTapped))
    tapGesture.delegate = self
    maskView.addGestureRecognizer(tapGesture)
    
    inputMaskView = maskView
}
```

### 2. 蒙版点击处理

```swift
@objc private func inputMaskTapped() {
    print("[VideoComment] 输入框蒙版被点击")
    
    // 根据当前状态决定行为
    switch inputPanelState {
    case .keyboard:
        // 普通键盘状态：收起键盘
        inputPanelState = .none
    case .atPanel:
        // @面板状态：收起@面板，但保持键盘
        resetAtPanelState()
        inputPanelState = .keyboard
    case .emoji:
        // 表情面板状态：收起表情面板
        inputPanelState = .none
    case .none:
        // 已收起状态：不做任何操作
        break
    }
}
```

### 3. 状态驱动的可见性管理

```swift
private func updateInputMaskVisibility() {
    guard let maskView = inputMaskView else { return }
    
    switch inputPanelState {
    case .keyboard, .emoji, .atPanel:
        // 有输入相关面板时显示蒙版，防止误触评论列表
        maskView.isHidden = false
        // 确保层级正确
        view.bringSubviewToFront(maskView)
        view.bringSubviewToFront(containerView)
        
    case .none:
        // 无输入面板时隐藏蒙版，允许正常交互评论列表
        maskView.isHidden = true
    }
}
```

### 4. 手势代理优化

```swift
func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
    let gestureView = gestureRecognizer.view
    
    // 输入框蒙版手势：允许执行
    if gestureView == inputMaskView {
        return true
    }
    
    // @面板蒙版手势：允许执行
    if gestureView == atPanelMaskView {
        return true
    }
    
    // 背景遮罩手势：有输入面板时不处理，由输入框蒙版处理
    if gestureView == backgroundView {
        if inputPanelState != .none {
            return false
        }
        return touch.view == self.backgroundView
    }
    
    return touch.view == self.backgroundView
}
```

## 交互逻辑

### 状态与蒙版显示关系

| 输入状态 | 输入框蒙版 | 点击蒙版行为 |
|---------|-----------|-------------|
| `.none` | 隐藏 | 无操作 |
| `.keyboard` | 显示 | 收起键盘 |
| `.emoji` | 显示 | 收起表情面板 |
| `.atPanel` | 显示 | 收起@面板，保持键盘 |

### 用户体验优化

1. **防止误操作**：
   - 编辑时蒙版阻止点击评论列表
   - 避免意外触发评论交互

2. **直观的交互**：
   - 点击输入区域外收起相应面板
   - @面板特殊处理，保持键盘弹出

3. **层级管理**：
   - 蒙版在评论列表之上
   - 输入相关UI在蒙版之上
   - 确保正常的输入交互

## 视图层级结构

```
view
├── backgroundView (背景遮罩，用于关闭整个弹窗)
├── inputMaskView (输入框蒙版，用于收起输入面板)
└── containerView (内容容器)
    ├── headerView (标题栏)
    ├── tableView (评论列表)
    ├── atPanelMaskView (@面板蒙版)
    ├── atPanelView (@面板)
    ├── decorativeInputBar (装饰输入框)
    └── commentInputBar (真实输入框)
```

## 测试验证

请测试以下场景：

1. ✅ **点击输入框** → 键盘弹出，蒙版显示
2. ✅ **点击蒙版** → 键盘收起，蒙版隐藏
3. ✅ **键盘弹出时点击评论** → 无反应，被蒙版阻挡
4. ✅ **@面板显示时点击蒙版** → @面板收起，键盘保持
5. ✅ **表情面板显示时点击蒙版** → 表情面板收起
6. ✅ **无输入面板时** → 蒙版隐藏，评论列表正常交互

## 技术要点

1. **透明蒙版**：不影响视觉效果，只阻挡交互
2. **状态驱动**：根据输入状态自动显示/隐藏
3. **手势优先级**：输入框蒙版优先于背景遮罩
4. **层级管理**：确保输入UI在蒙版之上
5. **区域限制**：蒙版不覆盖输入框区域

现在用户在编辑时不会误触评论列表，同时提供了直观的收起输入面板的方式。
