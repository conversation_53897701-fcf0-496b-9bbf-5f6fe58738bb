# 动态蒙版和@面板位置修复

## 问题分析

### 用户反馈的问题：
1. **输入框被红色蒙版遮住**：键盘弹起后，输入框移动到键盘上方，但红色蒙版也覆盖了这个区域
2. **@面板没有正确显示**：输入@后只看到蓝色蒙版，没看到白色@面板
3. **@面板位置错误**：应该在真实输入框上方，而不是固定位置

### 根本原因：
1. **蒙版位置静态**：蒙版使用固定位置，没有跟随输入框的动态移动
2. **@面板位置固定**：@面板位置固定，没有跟随真实输入框位置
3. **层级管理错误**：@面板层级管理使用了错误的父视图

## 修复方案

### 1. 动态输入框蒙版

**问题**：输入框移动到键盘上方后，蒙版仍然覆盖原位置，遮挡了移动后的输入框

**修复方案**：
```swift
// 添加动态约束
private var inputMaskBottomConstraint: NSLayoutConstraint?

// 创建时使用动态约束
inputMaskBottomConstraint = maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -200)

// 键盘显示时动态调整
let inputBarHeight: CGFloat = 132 // 输入框高度
inputMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 20) // 额外20pt间距
```

### 2. 动态@面板位置

**问题**：@面板位置固定，没有跟随真实输入框移动

**修复方案**：
```swift
// 添加动态约束
private var atPanelBottomConstraint: NSLayoutConstraint?

// 创建时跟随输入框位置
atPanelBottomConstraint = panel.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -lastKeyboardHeight - 132)

// 键盘显示时动态调整
atPanelBottomConstraint?.constant = -(keyboardHeight + inputBarHeight)
```

### 3. 动态@面板蒙版

**问题**：@面板蒙版位置固定，没有跟随@面板移动

**修复方案**：
```swift
// 添加动态约束
private var atPanelMaskBottomConstraint: NSLayoutConstraint?

// 创建时跟随@面板位置
atPanelMaskBottomConstraint = maskView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -(lastKeyboardHeight + 132 + 104))

// 键盘显示时动态调整
atPanelMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 104) // 104是@面板高度
```

### 4. 修复层级管理

**问题**：@面板层级管理使用了错误的父视图

**修复前**：
```swift
containerView.bringSubviewToFront(panel) // 错误：panel在view中，不在containerView中
```

**修复后**：
```swift
view.bringSubviewToFront(mask)  // 先显示蒙版
view.bringSubviewToFront(panel) // 再显示@面板，确保在蒙版之上
```

## 动态布局逻辑

### 键盘收起状态：
```
┌─────────────────────────────────────┐
│ 评论列表区域                        │ ← 无蒙版
│                                     │
├─────────────────────────────────────┤
│ 装饰输入框（底部）                  │ ← 显示
└─────────────────────────────────────┘
```

### 键盘弹起状态：
```
┌─────────────────────────────────────┐
│ 评论列表区域                        │ ← 红色蒙版覆盖
├─────────────────────────────────────┤ ← 蒙版底部（动态调整）
│ 真实输入框（键盘上方）              │ ← 不被遮挡
├─────────────────────────────────────┤
│ 键盘区域                            │
└─────────────────────────────────────┘
```

### @面板显示状态：
```
┌─────────────────────────────────────┐
│ 评论列表区域                        │ ← 蓝色蒙版覆盖
├─────────────────────────────────────┤ ← 蒙版底部（动态调整）
│ @面板（104pt高）                    │ ← 白色@面板显示
├─────────────────────────────────────┤
│ 真实输入框（键盘上方）              │ ← 不被遮挡
├─────────────────────────────────────┤
│ 键盘区域                            │
└─────────────────────────────────────┘
```

## 关键计算公式

### 输入框蒙版底部位置：
```swift
inputMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 20)
```
- `keyboardHeight`：键盘高度
- `inputBarHeight`：输入框高度（132pt）
- `20`：额外间距，确保不遮挡输入框

### @面板底部位置：
```swift
atPanelBottomConstraint?.constant = -(keyboardHeight + inputBarHeight)
```
- 直接在输入框上方，无额外间距

### @面板蒙版底部位置：
```swift
atPanelMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 104)
```
- 在@面板上方，104pt是@面板高度

## 预期修复效果

### ✅ 修复后应该实现：

1. **输入框不被遮挡**：
   - 键盘弹起时，红色蒙版动态调整位置
   - 输入框移动到键盘上方后仍然可以正常交互
   - 蒙版只覆盖评论列表区域

2. **@面板正确显示**：
   - 输入@后，白色@面板显示在真实输入框上方
   - @面板跟随输入框位置动态调整
   - @面板高度104pt，背景白色

3. **@面板蒙版正确**：
   - 蓝色蒙版覆盖评论列表区域
   - 蒙版不遮挡@面板和输入框
   - 点击蒙版收起@面板

4. **层级关系正确**：
   - 评论列表（最底层）
   - 输入框蒙版（红色）
   - @面板蒙版（蓝色，@面板时显示）
   - @面板（白色，@面板时显示）
   - 输入框（最上层）

## 测试验证

### 1. 测试输入框蒙版动态调整
1. 点击输入框，键盘弹起
2. 红色蒙版应该出现，但不遮挡移动后的输入框
3. 输入框应该可以正常输入文字

### 2. 测试@面板显示
1. 在输入框中输入@符号
2. 应该看到白色@面板出现在输入框上方
3. 蓝色蒙版应该覆盖评论列表区域
4. @面板中应该显示用户列表

### 3. 测试@面板交互
1. 点击@面板中的用户 → 插入@用户名
2. 点击蓝色蒙版 → @面板收起，键盘保持
3. 点击红色蒙版 → 键盘收起

### 4. 测试不同键盘高度
1. 切换不同输入法（键盘高度不同）
2. @面板和蒙版位置应该正确调整
3. 输入框始终不被遮挡

现在蒙版和@面板都应该能够动态跟随输入框位置，不再遮挡输入框，@面板也应该正确显示！
