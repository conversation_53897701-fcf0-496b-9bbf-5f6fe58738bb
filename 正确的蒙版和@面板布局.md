# 正确的蒙版和@面板布局

## 需求理解修正

### 用户的正确需求：
1. **输入框蒙版**：只覆盖评论列表区域，不遮挡输入框和按钮，确保输入框可以正常交互
2. **@面板位置**：固定在输入框上方，不跟随键盘移动
3. **@面板蒙版**：覆盖评论列表区域，不遮挡@面板和输入框

### 之前的错误理解：
- ❌ 蒙版遮挡输入框导致无法交互
- ❌ @面板跟随键盘移动到键盘上方

## 正确的布局设计

### 视图区域划分：

```
┌─────────────────────────────────────┐
│ 状态栏 + 安全区域                    │
├─────────────────────────────────────┤ ← view.safeAreaLayoutGuide.topAnchor
│ 空白区域 (60pt)                     │
├─────────────────────────────────────┤ ← containerView.topAnchor
│                                     │
│ 评论列表区域                        │ ← 蒙版覆盖区域（红色/蓝色）
│ (被蒙版阻挡交互)                    │ ← 不影响输入框交互
│                                     │
├─────────────────────────────────────┤ ← 蒙版底部边界
│ @面板 (104pt高)                     │ ← 固定在输入框上方
├─────────────────────────────────────┤ ← 输入框顶部
│ 输入框区域 + 按钮                   │ ← 始终可交互
├─────────────────────────────────────┤ ← 键盘顶部
│ 键盘区域                            │ ← 键盘高度变化
└─────────────────────────────────────┘
```

## 修复后的约束

### 1. 输入框蒙版约束：
```swift
// 只覆盖评论列表区域，预留更多空间给输入框
maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60)
maskView.leftAnchor.constraint(equalTo: view.leftAnchor)
maskView.rightAnchor.constraint(equalTo: view.rightAnchor)
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -200) // 预留200pt给输入框区域
```

### 2. @面板约束：
```swift
// 固定在输入框上方，不跟随键盘
panel.leftAnchor.constraint(equalTo: view.leftAnchor)
panel.rightAnchor.constraint(equalTo: view.rightAnchor)
panel.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120) // 固定位置
panel.heightAnchor.constraint(equalToConstant: 104)
```

### 3. @面板蒙版约束：
```swift
// 覆盖评论列表区域，到@面板顶部
maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60)
maskView.leftAnchor.constraint(equalTo: view.leftAnchor)
maskView.rightAnchor.constraint(equalTo: view.rightAnchor)
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -224) // 120+104，到@面板顶部
```

## 关键修复点

### 1. 输入框蒙版不遮挡输入框
**修复前**：
```swift
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120)
```
- 问题：-120可能不够，仍然遮挡输入框

**修复后**：
```swift
maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -200)
```
- 解决：预留200pt空间，确保输入框和按钮可交互

### 2. @面板固定在输入框上方
**修复前**：
```swift
atPanelBottomConstraint = panel.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -lastKeyboardHeight)
```
- 问题：跟随键盘移动，位置不固定

**修复后**：
```swift
panel.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -120)
```
- 解决：固定在输入框上方，不受键盘影响

### 3. 移除键盘跟随逻辑
**修复前**：
```swift
atPanelBottomConstraint?.constant = -keyboardHeight
```
- 问题：@面板跟随键盘移动

**修复后**：
```swift
// @面板固定在输入框上方，不跟随键盘移动
```
- 解决：@面板位置固定

## 预期效果

### ✅ 修复后应该实现：

1. **输入框完全可交互**：
   - 红色蒙版不遮挡输入框
   - 输入框、@按钮、表情按钮等都可以正常点击
   - 蒙版只阻挡评论列表交互

2. **@面板固定位置**：
   - @面板始终在输入框上方
   - 不跟随键盘高度变化
   - 高度固定104pt

3. **@面板蒙版正确**：
   - 蓝色蒙版覆盖评论列表区域
   - 不遮挡@面板和输入框
   - 点击蒙版收起@面板

4. **层级关系正确**：
   - 评论列表（最底层）
   - 输入框蒙版（红色，阻挡评论列表）
   - @面板蒙版（蓝色，@面板时显示）
   - @面板（白色，@面板时显示）
   - 输入框（最上层，始终可交互）

## 测试验证

### 1. 测试输入框交互
1. 点击输入框 → 应该能正常弹出键盘
2. 点击@按钮 → 应该能正常插入@符号
3. 点击表情按钮 → 应该能正常弹出表情面板
4. 红色蒙版显示时，输入框仍然可以正常使用

### 2. 测试@面板位置
1. 输入@符号 → @面板出现在输入框上方
2. 键盘高度变化 → @面板位置不变
3. @面板高度固定104pt
4. 蓝色蒙版覆盖评论列表区域

### 3. 测试蒙版交互
1. 键盘状态：点击红色蒙版 → 键盘收起
2. @面板状态：点击蓝色蒙版 → @面板收起，键盘保持
3. 评论列表被蒙版阻挡，无法点击头像等

### 4. 测试边界情况
1. 快速切换键盘和@面板 → 位置应该稳定
2. 旋转屏幕 → 布局应该正确
3. 不同键盘高度 → @面板位置不受影响

## 常量说明

- **60pt**：containerView顶部偏移
- **120pt**：输入框区域预留高度
- **104pt**：@面板固定高度
- **200pt**：输入框蒙版底部偏移（120+80额外空间）
- **224pt**：@面板蒙版底部偏移（120+104）

现在布局应该完全正确：输入框可交互，@面板固定在输入框上方！
