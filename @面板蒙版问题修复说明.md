# @面板蒙版问题修复说明

## 问题描述

原有的@面板蒙版机制存在以下问题：

1. **蒙版没有遮住评论列表**：点击评论列表内的空白会收起键盘
2. **有效拦截区域太小**：只有顶部中间一小块才能拦截并收起@面板
3. **@面板空白部分触发收起键盘**：@面板内部的空白区域仍然会触发收起键盘

## 根本原因分析

1. **蒙版位置错误**：蒙版被放在了`view`层级，而不是`containerView`内部
2. **蒙版覆盖范围不正确**：没有正确覆盖评论列表区域
3. **@面板点击穿透**：@面板内部的点击会穿透到下层，触发意外的手势
4. **手势代理逻辑复杂**：复杂的区域判断导致逻辑混乱

## 修复方案

### 1. 重新设计蒙版位置和范围

**修复前**：
```swift
// 蒙版在view层级，覆盖整个视图
view.insertSubview(maskView, belowSubview: containerView)
NSLayoutConstraint.activate([
    maskView.topAnchor.constraint(equalTo: view.topAnchor),
    maskView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
])
```

**修复后**：
```swift
// 蒙版在containerView内部，正确覆盖评论区域
containerView.addSubview(maskView)
NSLayoutConstraint.activate([
    maskView.topAnchor.constraint(equalTo: containerView.topAnchor),
    maskView.bottomAnchor.constraint(equalTo: commentInputBar.topAnchor)
])
```

### 2. @面板层级和防穿透处理

**新增功能**：
```swift
// @面板添加到containerView内部，确保在蒙版之上
containerView.addSubview(panel)

// 为@面板添加手势，防止点击穿透
let panelTapGesture = UITapGestureRecognizer(target: self, action: #selector(atPanelTapped))
panel.addGestureRecognizer(panelTapGesture)

// @面板内部点击处理（防止穿透）
@objc private func atPanelTapped() {
    // 不做任何操作，防止穿透到下层
}
```

### 3. 简化手势代理逻辑

**修复前**：复杂的区域判断和多重条件
**修复后**：简化逻辑，依赖正确的视图层级

```swift
func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
    // @面板打开时，只允许背景遮罩的点击
    if inputPanelState == .atPanel {
        return touch.view == self.backgroundView
    }
    return touch.view == self.backgroundView
}
```

### 4. 确保视图层级正确

```swift
// 显示时确保@面板在蒙版之上
if let panel = atPanelView, let mask = atPanelMaskView {
    containerView.bringSubviewToFront(panel)
}
```

## 修复后的交互逻辑

### @面板打开状态下的点击行为：

1. **点击评论列表区域** → 收起@面板，保持键盘 ✅
2. **点击标题栏区域** → 收起@面板，保持键盘 ✅
3. **点击@面板内部** → 无操作，保持当前状态 ✅
4. **点击@面板用户Cell** → 选择用户，插入@用户名 ✅
5. **点击输入框区域** → 无操作，保持当前状态 ✅
6. **点击背景遮罩** → 关闭整个评论弹窗 ✅

### 视图层级结构：

```
view
├── backgroundView (背景遮罩)
└── containerView
    ├── headerView (标题栏)
    ├── tableView (评论列表)
    ├── atPanelMaskView (透明蒙版，覆盖评论区域)
    ├── atPanelView (@面板，在蒙版之上)
    ├── decorativeInputBar (装饰输入框)
    └── commentInputBar (真实输入框)
```

## 技术要点

1. **蒙版位置**：放在`containerView`内部，确保正确覆盖评论区域
2. **@面板防穿透**：添加专门的手势处理，防止点击穿透
3. **视图层级**：确保@面板在蒙版之上，蒙版在评论列表之上
4. **手势简化**：移除复杂的区域判断，依赖正确的视图层级

## 测试验证

请测试以下场景：

1. ✅ 点击@按钮 → @面板显示
2. ✅ 点击评论列表空白区域 → @面板收起，键盘保持
3. ✅ 点击标题栏区域 → @面板收起，键盘保持
4. ✅ 点击@面板内部空白 → 无操作
5. ✅ 点击@面板用户头像 → 选择用户
6. ✅ 点击输入框 → 无操作
7. ✅ 点击背景遮罩 → 关闭评论弹窗

现在@面板的蒙版机制应该能正确工作，提供精确的交互控制。
