# 输入框蒙版空隙修复

## 问题描述

**用户反馈**：红色蒙版（输入框蒙版）在输入框上方约50pt的位置有空隙，这个空隙区域没有被蒙版覆盖，用户可以直接点击到下方的评论列表。

## 问题分析

### 原始计算错误：

**键盘弹起时的蒙版位置**：
```swift
inputMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 20) // 额外20pt间距
```

**问题**：
- 输入框通过 `commentInputBarBottomConstraint?.constant = -keyboardHeight` 移动到键盘上方
- 输入框的实际顶部位置是：屏幕底部向上 `keyboardHeight + inputBarHeight` 的位置
- 蒙版底部位置是：屏幕底部向上 `keyboardHeight + inputBarHeight + 20` 的位置
- **结果**：蒙版底部和输入框顶部之间有20pt的空隙 ❌

### 视觉表现：

```
┌─────────────────────────────────────┐
│ 评论列表区域                        │ ← 红色蒙版覆盖
├─────────────────────────────────────┤ ← 蒙版底部
│ 空隙区域（约50pt）                  │ ← 可以点击评论列表 ❌
├─────────────────────────────────────┤ ← 输入框顶部
│ 真实输入框                          │
├─────────────────────────────────────┤
│ 键盘区域                            │
└─────────────────────────────────────┘
```

## 修复方案

### 1. 修正键盘弹起时的蒙版位置

**修复前**：
```swift
inputMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 20) // 有20pt空隙
```

**修复后**：
```swift
inputMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight) // 紧贴输入框顶部
```

### 2. 修正初始状态的蒙版位置

**修复前**：
```swift
inputMaskBottomConstraint = maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -200) // 硬编码200pt
```

**修复后**：
```swift
let decorativeBarHeight: CGFloat = 65 + WindowUtil.safeAreaBottom
inputMaskBottomConstraint = maskView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -decorativeBarHeight) // 紧贴装饰输入框顶部
```

## 修复后的布局逻辑

### 键盘收起状态：
```
┌─────────────────────────────────────┐
│ 评论列表区域                        │ ← 红色蒙版覆盖
├─────────────────────────────────────┤ ← 蒙版底部 = 装饰输入框顶部
│ 装饰输入框（65 + safeAreaBottom）   │ ← 不被遮挡
└─────────────────────────────────────┘
```

### 键盘弹起状态：
```
┌─────────────────────────────────────┐
│ 评论列表区域                        │ ← 红色蒙版覆盖
├─────────────────────────────────────┤ ← 蒙版底部 = 输入框顶部（无空隙）
│ 真实输入框（132pt高）               │ ← 不被遮挡
├─────────────────────────────────────┤
│ 键盘区域                            │
└─────────────────────────────────────┘
```

## 关键计算公式

### 初始状态（键盘收起）：
```swift
let decorativeBarHeight: CGFloat = 65 + WindowUtil.safeAreaBottom
inputMaskBottomConstraint?.constant = -decorativeBarHeight
```
- 蒙版底部 = 装饰输入框顶部
- 无空隙，完全覆盖评论列表区域

### 键盘弹起状态：
```swift
let inputBarHeight: CGFloat = 132 // 真实输入框高度
inputMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight)
```
- 蒙版底部 = 真实输入框顶部
- 无空隙，完全覆盖评论列表区域

## 预期修复效果

### ✅ 修复后应该实现：

1. **无空隙覆盖**：
   - 红色蒙版紧贴输入框顶部，无任何空隙
   - 评论列表完全被蒙版覆盖，无法点击

2. **动态调整正确**：
   - 键盘弹起时，蒙版跟随输入框移动
   - 键盘收起时，蒙版回到装饰输入框顶部

3. **输入框不被遮挡**：
   - 蒙版底部正好在输入框顶部
   - 输入框完全可见和可交互

## 测试验证

### 1. 测试键盘收起状态
1. 应用启动，键盘收起
2. 红色蒙版应该不可见（inputPanelState = .none）
3. 点击输入框，红色蒙版出现，紧贴装饰输入框顶部

### 2. 测试键盘弹起状态
1. 点击输入框，键盘弹起
2. 红色蒙版应该紧贴真实输入框顶部，无空隙
3. 尝试点击输入框上方的任何区域，应该无反应（被蒙版阻挡）

### 3. 测试不同键盘高度
1. 切换不同输入法（键盘高度不同）
2. 红色蒙版应该正确调整位置
3. 始终紧贴输入框顶部，无空隙

### 4. 测试边界情况
1. 快速点击输入框多次
2. 蒙版位置应该稳定，无闪烁
3. 无论何时都不应该有空隙

## 调试提示

如果仍然有空隙，可能的原因：

1. **输入框高度不准确**：
   - 检查 `inputBarHeight = 132` 是否正确
   - 可以通过调试输出实际输入框frame来确认

2. **键盘高度获取错误**：
   - 检查 `keyboardHeight` 是否正确获取
   - 不同设备键盘高度可能不同

3. **约束更新时机**：
   - 确保约束更新在正确的时机
   - 确保 `view.layoutIfNeeded()` 被调用

## 验证方法

可以临时添加调试代码：
```swift
print("键盘高度: \(keyboardHeight)")
print("输入框高度: \(inputBarHeight)")
print("蒙版底部约束: \(inputMaskBottomConstraint?.constant ?? 0)")
print("输入框底部约束: \(commentInputBarBottomConstraint?.constant ?? 0)")
```

现在红色蒙版应该紧贴输入框顶部，完全没有空隙！
