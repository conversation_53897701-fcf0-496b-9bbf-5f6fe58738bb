# @面板智能显示逻辑优化

## 需求分析

根据你的需求，我们实现了以下智能@面板逻辑：

### 核心流程
1. **@触发** → 输入@符号 + 开始监听
2. **@+空白** → 调用推荐接口
3. **@+关键词** → 调用搜索接口  
4. **无数据** → 不弹出@面板
5. **有数据** → 弹出@面板
6. **选择用户** → @用户名变成整体高亮
7. **整体删除** → 删除整个@用户名块
8. **停止监听** → 直到下一个@出现

## 技术实现

### 1. 状态管理优化

```swift
// 新增状态变量
private var isAtListening: Bool = false      // 是否在监听@输入
private var isAtPanelVisible: Bool = false   // @面板是否可见
private var atSearchWorkItem: DispatchWorkItem? // @搜索防抖

// 推荐用户缓存
private var cachedRecommendUsers: [UserSearchResultsItem] = []
private var recommendCacheTime: Date?
```

### 2. @监听状态管理

```swift
// 开始@监听
private func startAtListening(at location: Int) {
    isAtListening = true
    isMentioning = true
    mentionStartLocation = location
    mentionKeyword = ""
    inputPanelState = .keyboard // 保持键盘状态
}

// 停止@监听
private func stopAtListening() {
    isAtListening = false
    isMentioning = false
    isAtPanelVisible = false
    hideAtPanel()
    // 恢复占位文字
}
```

### 3. 智能接口调用

```swift
private func requestAtUsers(keyword: String, isRecommend: Bool) {
    // 推荐请求优先使用缓存
    if isRecommend, let cachedUsers = getCachedRecommendUsers() {
        handleAtSearchResult(cachedUsers, keyword: keyword, isRecommend: true)
        return
    }
    
    // 防抖处理
    atSearchWorkItem?.cancel()
    let workItem = DispatchWorkItem { [weak self] in
        self?.searchMentionUsers(keyword: keyword, page: 0, isRecommend: isRecommend)
    }
    
    // 推荐请求立即执行，搜索请求防抖
    let delay = isRecommend ? 0.1 : 0.5
    DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
}
```

### 4. 智能面板显示逻辑

```swift
private func handleAtSearchResult(_ users: [UserSearchResultsItem], keyword: String, isRecommend: Bool) {
    if users.isEmpty {
        // 无数据：隐藏面板，但保持监听状态
        if isAtListening {
            hideAtPanel()
            isAtPanelVisible = false
        }
    } else {
        // 有数据：显示面板
        if isAtListening {
            showAtPanel()
            isAtPanelVisible = true
            inputPanelState = .atPanel
        }
    }
}
```

### 5. 优化的输入处理

```swift
func textViewDidChange(_ textView: UITextView) {
    if isAtListening {
        // 检查@符号是否还存在
        if mentionStartLocation >= nsText.length || nsText.character(at: mentionStartLocation) != 64 {
            stopAtListening() // @被删除，停止监听
            return
        }
        
        // 计算关键词变化
        let newKeyword = extractKeywordAfterAt()
        if newKeyword != mentionKeyword {
            mentionKeyword = newKeyword
            
            if newKeyword.isEmpty {
                requestAtUsers(keyword: "", isRecommend: true)  // 推荐
            } else {
                requestAtUsers(keyword: newKeyword, isRecommend: false) // 搜索
            }
        }
    } else {
        // 检查新输入的@
        if isNewAtInput() {
            startAtListening(at: cursor - 1)
            requestAtUsers(keyword: "", isRecommend: true)
        }
    }
}
```

## 用户体验优化

### 1. 缓存机制
- **推荐用户缓存**：5分钟内重复@请求直接使用缓存
- **减少网络请求**：提升响应速度

### 2. 防抖优化
- **推荐请求**：0.1秒延迟（几乎立即）
- **搜索请求**：0.5秒防抖（避免频繁请求）

### 3. 智能显示
- **无数据不显示**：避免空白面板的困扰
- **有数据才显示**：确保面板内容有意义
- **保持监听状态**：即使面板隐藏也继续监听输入

### 4. 状态同步
- **选择完成停止监听**：避免后续输入干扰
- **整体删除重置状态**：确保状态一致性

## 交互流程图

```
用户输入@ 
    ↓
开始监听(@+空白)
    ↓
请求推荐用户
    ↓
有推荐数据? ──No──→ 隐藏面板，继续监听
    ↓ Yes
显示@面板
    ↓
用户继续输入关键词
    ↓
请求搜索用户
    ↓
有搜索数据? ──No──→ 隐藏面板，继续监听
    ↓ Yes
更新@面板内容
    ↓
用户选择用户 ──→ 插入@用户名，停止监听
```

## 优势总结

1. **智能显示**：只有在有数据时才显示面板
2. **流畅体验**：缓存和防抖提升响应速度
3. **状态清晰**：明确的监听和显示状态管理
4. **资源节约**：避免无意义的接口请求
5. **用户友好**：符合用户直觉的交互逻辑

这个优化方案完全符合你的需求描述，提供了流畅且智能的@用户体验。
