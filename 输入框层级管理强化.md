# 输入框层级管理强化

## 问题描述

输入框在键盘弹起后出现在红色蒙版的下层，导致输入框不可交互。需要确保输入框始终在蒙版上层。

## 问题分析

### 根本原因：
1. **布局更新影响层级**：`view.layoutIfNeeded()`可能会影响视图的层级顺序
2. **时机问题**：层级调整在布局更新之前，布局更新后层级可能被重置
3. **异步操作**：键盘弹起是异步过程，层级调整可能被其他操作覆盖

### 问题场景：
```
1. 用户点击输入框
2. inputPanelState = .keyboard → updateInputMaskVisibility() → 设置层级
3. keyboardWillShow → 更新约束 → view.layoutIfNeeded()
4. 布局更新过程中，层级顺序可能被重置 ❌
5. 输入框出现在蒙版下层，不可交互 ❌
```

## 解决方案

### 1. 添加强制层级确保方法

**新增方法**：
```swift
private func ensureInputViewsAboveMask() {
    guard let maskView = inputMaskView, !maskView.isHidden else { return }
    
    // 强制确保输入框在蒙版之上，防止布局更新后层级混乱
    view.bringSubviewToFront(maskView)           // 1. 全屏红色蒙版
    view.bringSubviewToFront(decorativeInputBar) // 2. 装饰输入框（在蒙版之上）
    view.bringSubviewToFront(commentInputBar)    // 3. 真实输入框（在蒙版之上）
    
    // @面板相关视图也要确保在正确层级
    if let atPanelMask = atPanelMaskView, !atPanelMask.isHidden {
        view.bringSubviewToFront(atPanelMask)    // 4. @面板蒙版
    }
    if let atPanel = atPanelView, !atPanel.isHidden {
        view.bringSubviewToFront(atPanel)        // 5. @面板（最上层）
    }
}
```

### 2. 在关键时机调用层级确保

#### 时机1：键盘弹起动画完成后
```swift
UIView.animate(withDuration: 0.3) {
    self.view.layoutIfNeeded()
} completion: { _ in
    // 布局完成后，重新确保层级顺序
    self.ensureInputViewsAboveMask()
}
```

#### 时机2：状态变化时异步确保
```swift
private func updateInputMaskVisibility() {
    // ... 原有逻辑 ...
    
    // 延迟一帧确保层级，防止其他操作影响层级顺序
    DispatchQueue.main.async {
        self.ensureInputViewsAboveMask()
    }
}
```

#### 时机3：@面板显示时
```swift
// 使用统一的层级管理方法
ensureInputViewsAboveMask()

// 确保@面板相关视图在最上层
view.bringSubviewToFront(mask)   // @面板蒙版
view.bringSubviewToFront(panel)  // @面板
```

## 修复后的完整流程

### 键盘弹起流程：
```
1. 用户点击输入框
2. inputPanelState = .keyboard
3. updateInputMaskVisibility() → 设置初始层级
4. DispatchQueue.main.async → ensureInputViewsAboveMask() (异步确保)
5. keyboardWillShow → 更新约束
6. UIView.animate → view.layoutIfNeeded() (布局更新)
7. animation completion → ensureInputViewsAboveMask() (完成后确保)
8. 输入框确保在蒙版之上 ✅
```

### @面板显示流程：
```
1. 用户输入@符号
2. showAtPanel() → 创建@面板
3. ensureInputViewsAboveMask() → 确保基础层级
4. bringSubviewToFront(@面板) → 确保@面板在最上层
5. 所有视图都在正确层级 ✅
```

## 层级管理策略

### 1. 多重保障
- **初始设置**：状态变化时设置层级
- **异步确保**：延迟一帧再次确保
- **完成确保**：动画完成后最终确保

### 2. 统一方法
- 使用`ensureInputViewsAboveMask()`统一管理层级
- 避免在多处重复层级设置代码
- 确保层级设置的一致性

### 3. 条件检查
```swift
guard let maskView = inputMaskView, !maskView.isHidden else { return }
```
- 只在蒙版显示时调整层级
- 避免不必要的操作

## 预期修复效果

### ✅ 修复后应该实现：

1. **输入框始终可交互**：
   - 键盘弹起过程中，输入框始终在蒙版之上
   - 布局更新不会影响层级顺序
   - 输入框完全可见和可交互

2. **层级管理稳定**：
   - 多重保障确保层级正确
   - 异步操作不会影响层级
   - 动画过程中层级保持稳定

3. **@面板正常工作**：
   - @面板显示时层级正确
   - 输入框仍然在红色蒙版之上
   - @面板在最上层

## 测试验证

### 1. 测试键盘弹起层级
1. 点击输入框，观察键盘弹起过程
2. 输入框应该始终可见，不被红色蒙版遮挡 ✅
3. 输入框应该始终可以点击和输入 ✅
4. 红色蒙版应该覆盖评论列表，但不遮挡输入框 ✅

### 2. 测试快速操作
1. 快速点击输入框多次
2. 快速切换键盘和@面板
3. 层级应该始终保持正确 ✅

### 3. 测试@面板层级
1. 输入@符号，@面板显示
2. 输入框应该仍然可见可交互 ✅
3. @面板应该在最上层 ✅
4. 红色蒙版应该在最底层 ✅

### 4. 测试边界情况
1. 在键盘弹起动画过程中快速操作
2. 旋转屏幕时的层级保持
3. 应用进入后台再回到前台的层级保持

## 调试信息

修复后的日志输出：
```
[VideoComment] 显示全屏输入框蒙版，确保输入框在蒙版之上
[VideoComment] 强制确保输入框在蒙版之上 (异步)
[VideoComment] 强制确保输入框在蒙版之上 (动画完成)
```

## 关键技术点

### 1. 多时机保障
- 状态变化时
- 异步延迟时
- 动画完成时

### 2. 条件检查
- 检查蒙版是否存在和显示
- 检查@面板是否存在和显示

### 3. 统一管理
- 使用统一的方法管理层级
- 避免代码重复和不一致

现在输入框应该始终在红色蒙版之上，完全可见和可交互！
